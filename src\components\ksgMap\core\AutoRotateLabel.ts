/**
 * 自动旋转标签管理器 - AutoRotateLabel.ts
 *
 * 职责：
 * 1. 在自动旋转过程中识别最靠近屏幕中央的点
 * 2. 自动显示和隐藏该点的名称标签
 * 3. 管理标签的显示状态，避免与手动悬停冲突
 * 4. 提供平滑的标签切换动画效果
 */

import { Vector3, Vector2, Camera } from "three";
import { CSS2DObject } from "three/examples/jsm/Addons.js";
import ctx from "../ctx";
import { Point } from "../types";
import { labelEnter, labelLeave } from "../animation/label";

/**
 * 自动旋转标签管理器类
 * 负责在自动旋转时管理中央点的标签显示
 */
class AutoRotateLabel {
  /** 当前显示标签的点 */
  private currentPoint: Point | null = null;

  /** 当前显示的标签对象 */
  private currentLabel: CSS2DObject | null = null;

  /** 是否启用自动标签显示 */
  private enabled: boolean = false;

  /** 标签显示的防抖定时器 */
  private labelTimer: number | NodeJS.Timeout | null = null;

  /** 标签显示延迟时间（毫秒） */
  private readonly LABEL_DELAY = 500;

  /** 中央检测的容差范围（像素） */
  private readonly CENTER_TOLERANCE = 50;

  /**
   * 启用自动标签显示
   * 在自动旋转开始时调用
   */
  enable() {
    this.enabled = true;
  }

  /**
   * 禁用自动标签显示
   * 在自动旋转停止时调用，同时清理当前显示的标签
   */
  disable() {
    this.enabled = false;
    this.hideCurrentLabel();
    this.clearLabelTimer();
  }

  /**
   * 更新函数 - 每帧调用
   * 检测最靠近中央的点并管理标签显示
   *
   * @param camera 相机对象
   * @param containerWidth 容器宽度
   * @param containerHeight 容器高度
   */
  update(camera: Camera, containerWidth: number, containerHeight: number) {
    if (!this.enabled || !ctx.pointsMesh) return;

    // 查找最靠近屏幕中央的点
    const centerPoint = this.findCenterPoint(camera, containerWidth, containerHeight);

    // 如果中央点发生变化，更新标签显示
    if (centerPoint !== this.currentPoint) {
      this.updateLabelDisplay(centerPoint);
    }
  }

  /**
   * 查找最靠近屏幕中央的点
   *
   * @param camera 相机对象
   * @param containerWidth 容器宽度
   * @param containerHeight 容器高度
   * @returns 最靠近中央的点，如果没有找到则返回null
   */
  private findCenterPoint(camera: Camera, containerWidth: number, containerHeight: number): Point | null {
    if (!ctx.pointsMesh || !ctx.graph) return null;

    const screenCenter = new Vector2(0, 0); // 标准化设备坐标的中心点
    let closestPoint: Point | null = null;
    let minDistance = Infinity;

    // 遍历所有可见的点
    ctx.graph.pointsData.forEach((point) => {
      // 跳过聚焦点和不可见的点
      if (ctx.pointsMesh!.focusIndex === point.index) return;

      // 将3D坐标转换为屏幕坐标
      const screenPosition = this.worldToScreen(
        new Vector3(...point.coordinate),
        camera,
        containerWidth,
        containerHeight
      );

      // 计算到屏幕中心的距离
      const distance = screenCenter.distanceTo(screenPosition);

      // 只考虑在中央容差范围内的点
      const pixelDistance = distance * Math.min(containerWidth, containerHeight) / 2;
      if (pixelDistance <= this.CENTER_TOLERANCE && distance < minDistance) {
        minDistance = distance;
        closestPoint = point;
      }
    });

    return closestPoint;
  }

  /**
   * 将3D世界坐标转换为标准化屏幕坐标
   *
   * @param worldPosition 3D世界坐标
   * @param camera 相机对象
   * @param containerWidth 容器宽度
   * @param containerHeight 容器高度
   * @returns 标准化屏幕坐标 (-1到1)
   */
  private worldToScreen(
    worldPosition: Vector3,
    camera: Camera,
    containerWidth: number,
    containerHeight: number
  ): Vector2 {
    const vector = worldPosition.clone();
    vector.project(camera);
    return new Vector2(vector.x, vector.y);
  }

  /**
   * 更新标签显示
   * 处理标签的显示、隐藏和切换逻辑
   *
   * @param newPoint 新的中央点
   */
  private updateLabelDisplay(newPoint: Point | null) {
    // 清除之前的定时器
    this.clearLabelTimer();

    // 如果有当前标签且新点不同，先隐藏当前标签
    if (this.currentPoint && newPoint !== this.currentPoint) {
      this.hideCurrentLabel();
    }

    // 更新当前点
    this.currentPoint = newPoint;

    // 如果有新点，延迟显示标签（防抖）
    if (newPoint) {
      this.labelTimer = setTimeout(() => {
        this.showLabelForPoint(newPoint);
      }, this.LABEL_DELAY);
    }
  }

  /**
   * 为指定点显示标签
   *
   * @param point 要显示标签的点
   */
  private showLabelForPoint(point: Point) {
    if (!this.enabled || this.currentPoint !== point) return;

    // 创建标签元素
    const labelElement = document.createElement('div');
    labelElement.className = 'ksg-auto-rotate-label';
    labelElement.textContent = point.name;
    labelElement.style.cssText = `
      position: absolute;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      pointer-events: none;
      white-space: nowrap;
      z-index: 1000;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;

    // 创建CSS2D对象
    this.currentLabel = new CSS2DObject(labelElement);
    this.currentLabel.position.set(...point.coordinate);
    this.currentLabel.position.y += 0.5; // 稍微向上偏移

    // 添加到场景
    ctx.viewGroup?.add(this.currentLabel);

    // 播放进入动画
    labelEnter(this.currentLabel, 300);
  }

  /**
   * 隐藏当前显示的标签
   */
  private hideCurrentLabel() {
    if (this.currentLabel) {
      // 播放离开动画
      labelLeave(this.currentLabel, 200).then(() => {
        if (this.currentLabel) {
          ctx.viewGroup?.remove(this.currentLabel);
          this.currentLabel = null;
        }
      });
    }
    this.currentPoint = null;
  }

  /**
   * 清除标签显示定时器
   */
  private clearLabelTimer() {
    if (this.labelTimer) {
      clearTimeout(this.labelTimer as number);
      this.labelTimer = null;
    }
  }

  /**
   * 清理资源
   * 在组件销毁时调用
   */
  dispose() {
    this.disable();
    this.hideCurrentLabel();
  }
}

// 创建单例实例
const autoRotateLabel = new AutoRotateLabel();

export default autoRotateLabel;