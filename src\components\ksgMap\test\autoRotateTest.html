<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动旋转功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #28a745;
            background-color: #f8f9fa;
        }
        .test-item.pending {
            border-left-color: #ffc107;
        }
        .test-item.failed {
            border-left-color: #dc3545;
        }
        .status {
            font-weight: bold;
            margin-left: 10px;
        }
        .status.pass { color: #28a745; }
        .status.pending { color: #ffc107; }
        .status.fail { color: #dc3545; }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔄 自动旋转功能测试指南</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>本测试用于验证地图自动旋转和名称显示功能是否正常工作。请按照以下步骤进行测试：</p>
        </div>

        <div class="test-item">
            <strong>1. 基础自动旋转测试</strong>
            <span class="status pending">⏳ 待测试</span>
            <p>• 进入全局视图模式（双击地图空白区域）</p>
            <p>• 等待3-5秒不进行任何鼠标操作</p>
            <p>• 验证：地图开始自动旋转</p>
        </div>

        <div class="test-item">
            <strong>2. 鼠标交互停止旋转测试</strong>
            <span class="status pending">⏳ 待测试</span>
            <p>• 在自动旋转过程中移动鼠标</p>
            <p>• 验证：自动旋转立即停止</p>
            <p>• 再次等待3-5秒不操作</p>
            <p>• 验证：自动旋转重新开始</p>
        </div>

        <div class="test-item">
            <strong>3. 中央点名称显示测试</strong>
            <span class="status pending">⏳ 待测试</span>
            <p>• 在自动旋转过程中观察屏幕中央</p>
            <p>• 验证：最靠近中央的点显示名称标签</p>
            <p>• 验证：当其他点移动到中央时，标签会切换</p>
        </div>

        <div class="test-item">
            <strong>4. 标签动画效果测试</strong>
            <span class="status pending">⏳ 待测试</span>
            <p>• 观察标签的出现和消失</p>
            <p>• 验证：标签有平滑的淡入淡出动画</p>
            <p>• 验证：标签位置在点的上方</p>
        </div>

        <div class="test-item">
            <strong>5. 模式切换测试</strong>
            <span class="status pending">⏳ 待测试</span>
            <p>• 点击任意节点进入聚焦模式</p>
            <p>• 验证：自动旋转停止，自动标签消失</p>
            <p>• 双击进入全局视图</p>
            <p>• 验证：自动旋转和标签功能恢复</p>
        </div>

        <div class="test-item">
            <strong>6. 性能测试</strong>
            <span class="status pending">⏳ 待测试</span>
            <p>• 长时间观察自动旋转</p>
            <p>• 验证：帧率保持稳定，无明显卡顿</p>
            <p>• 验证：内存使用无异常增长</p>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔧 技术实现细节</h2>
        
        <h3>核心功能</h3>
        <div class="code-block">
✅ 自动旋转检测：3秒无鼠标操作后启动
✅ 中央点识别：实时计算最靠近屏幕中心的点
✅ 名称标签显示：使用CSS2D渲染器显示点名称
✅ 动画效果：平滑的标签淡入淡出动画
✅ 事件集成：与现有交互系统无缝集成
        </div>

        <h3>关键文件</h3>
        <div class="code-block">
📁 src/components/ksgMap/core/AutoRotateLabel.ts - 自动旋转标签管理器
📁 src/components/ksgMap/config/event.ts - 事件系统集成
📁 src/components/ksgMap/hooks/useRendererFrame.ts - 渲染循环更新
        </div>

        <h3>配置参数</h3>
        <div class="code-block">
⏱️ 无操作检测时间：3000ms (3秒)
📏 中央检测容差：50像素
⏳ 标签显示延迟：500ms
🎨 标签样式：黑色半透明背景，白色文字
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🐛 常见问题排查</h2>
        
        <div class="test-item">
            <strong>问题：自动旋转不启动</strong>
            <p>• 检查是否在全局视图模式</p>
            <p>• 确认等待时间足够（3秒以上）</p>
            <p>• 检查控制台是否有错误信息</p>
        </div>

        <div class="test-item">
            <strong>问题：标签不显示</strong>
            <p>• 检查点数据是否包含name字段</p>
            <p>• 确认CSS2D渲染器正常工作</p>
            <p>• 检查标签是否被其他元素遮挡</p>
        </div>

        <div class="test-item">
            <strong>问题：性能问题</strong>
            <p>• 检查中央点检测频率</p>
            <p>• 确认标签创建和销毁正常</p>
            <p>• 监控内存使用情况</p>
        </div>
    </div>

    <script>
        // 简单的测试状态管理
        function updateTestStatus(testNumber, status) {
            const testItems = document.querySelectorAll('.test-item');
            const item = testItems[testNumber - 1];
            const statusSpan = item.querySelector('.status');
            
            statusSpan.className = `status ${status}`;
            switch(status) {
                case 'pass':
                    statusSpan.textContent = '✅ 通过';
                    item.className = 'test-item';
                    break;
                case 'fail':
                    statusSpan.textContent = '❌ 失败';
                    item.className = 'test-item failed';
                    break;
                case 'pending':
                    statusSpan.textContent = '⏳ 待测试';
                    item.className = 'test-item pending';
                    break;
            }
        }

        // 示例：更新测试状态
        // updateTestStatus(1, 'pass');
        
        console.log('🔄 自动旋转功能测试页面已加载');
        console.log('请在实际的KsgMap组件中进行功能测试');
    </script>
</body>
</html>
